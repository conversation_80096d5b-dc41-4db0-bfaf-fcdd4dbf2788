<template>
  <div>
    <!-- Header -->
    <div
      class="flex flex-column md-flex justify-content-between align-items-start md:align-items-center mb-6 gap-4"
    >
      <div>
        <h1 class="text-3xl font-bold text-color mb-2">Project Registry</h1>
        <p class="text-color-secondary">Manage and view all registered EBP projects</p>
      </div>

      <div class="flex gap-4">
        <Button
          @click="refreshProjects"
          icon="pi pi-refresh"
          label="Refresh"
          outlined
          :loading="projectsStore.loading"
        />
        <Button @click="$router.push('/register')" icon="pi pi-plus" label="New Project" />
      </div>
    </div>

    <!-- Filters -->
    <Card class="mb-6">
      <template #content>
        <div class="grid md-grid-cols-3 gap-4">
          <div class="form-field">
            <label class="form-label">Filter by Department</label>
            <PrimeSelect
              v-model="filters.department"
              :options="departmentOptions"
              placeholder="All Departments"
              @change="applyFilters"
            />
          </div>

          <div class="form-field">
            <label class="form-label">Filter by Status</label>
            <PrimeSelect
              v-model="filters.status"
              :options="statusOptions"
              placeholder="All Statuses"
              @change="applyFilters"
            />
          </div>

          <div class="form-field">
            <label class="form-label">Filter by Type</label>
            <PrimeSelect
              v-model="filters.type"
              :options="typeOptions"
              placeholder="All Types"
              @change="applyFilters"
            />
          </div>
        </div>
      </template>
    </Card>

    <!-- Projects Table -->
    <Card>
      <template #content>
        <DataTable
          :value="filteredProjects"
          :paginator="true"
          :rows="20"
          :loading="projectsStore.loading"
          responsiveLayout="scroll"
          :globalFilterFields="['projectTitle', 'department', 'projectLeader.fullName']"
          v-model:filters="tableFilters"
          filterDisplay="menu"
          :sortField="'createdAt'"
          :sortOrder="-1"
        >
          <template #header>
            <div class="flex justify-content-between align-items-center">
              <span class="text-lg font-semibold"> Projects ({{ filteredProjects.length }}) </span>
              <span class="p-input-icon-left">
                <i class="pi pi-search" />
                <InputText
                  v-model="tableFilters['global'].value"
                  placeholder="Search projects..."
                />
              </span>
            </div>
          </template>

          <Column field="projectTitle" header="Project Title" sortable>
            <template #body="{ data }">
              <div class="project-title-cell">
                <div class="font-medium text-primary hover:text-primary-600 transition-colors">
                  {{ data.projectTitle }}
                </div>
                <div class="text-sm text-color-secondary">ID: {{ data.id }}</div>
              </div>
            </template>
          </Column>

          <Column field="department" header="Department" sortable />

          <Column field="projectLeader.fullName" header="Project Leader" sortable>
            <template #body="{ data }">
              <div class="font-medium">{{ data.projectLeader.fullName }}</div>
              <div class="text-sm text-color-secondary">{{ data.projectLeader.rank }}</div>
            </template>
          </Column>

          <Column field="progressStatus" header="Status" sortable>
            <template #body="{ data }">
              <Tag :value="data.progressStatus" :class="getStatusClass(data.progressStatus)" />
            </template>
          </Column>

          <Column field="projectType" header="Type" sortable />

          <Column field="commencementDate" header="Start Date" sortable>
            <template #body="{ data }">
              {{ formatDate(data.commencementDate) }}
            </template>
          </Column>

          <Column header="Actions" :exportable="false">
            <template #body="{ data }">
              <div class="flex gap-1">
                <Button
                  @click="editProject(data)"
                  icon="pi pi-pencil"
                  text
                  size="small"
                  v-tooltip="'Edit Project'"
                />
                <Button
                  @click="addCollaboratorToProject(data)"
                  icon="pi pi-user-plus"
                  text
                  size="small"
                  v-tooltip="'Add Collaborator'"
                />
                <Button
                  @click="removeCollaboratorFromProject(data)"
                  icon="pi pi-user-minus"
                  text
                  size="small"
                  v-tooltip="'Remove Collaborator'"
                />
                <Button
                  @click="transferLeadership(data)"
                  icon="pi pi-users"
                  text
                  size="small"
                  v-tooltip="'Transfer Leadership'"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </template>
    </Card>

    <!-- Verification Modal -->
    <VerificationModal
      :visible="verificationModal.visible"
      @update:visible="verificationModal.visible = $event"
      :project-id="verificationModal.projectId"
      :title="verificationModal.title"
      :message="verificationModal.message"
      @verified="handleVerificationSuccess"
      @cancelled="handleVerificationCancel"
    />

    <!-- Edit Project Dialog -->
    <Dialog
      :visible="editDialog.visible"
      @update:visible="editDialog.visible = $event"
      modal
      header="Edit Project"
      :style="{ width: '90vw', maxWidth: '800px' }"
    >
      <ProjectForm
        v-if="editDialog.visible"
        :initial-data="editDialog.project"
        :is-edit="true"
        :loading="editDialog.loading"
        @submit="handleEditSubmit"
        @cancel="editDialog.visible = false"
      />
    </Dialog>

    <!-- Collaborator Management Dialog -->
    <CollaboratorDialog
      :visible="collaboratorDialog.visible"
      @update:visible="collaboratorDialog.visible = $event"
      :mode="collaboratorDialog.mode"
      :project="collaboratorDialog.project"
      :loading="collaboratorDialog.loading"
      @action="handleCollaboratorAction"
      @cancel="collaboratorDialog.visible = false"
    />

    <!-- Project Detail Modal - Temporarily disabled for debugging -->
    <!-- <ProjectDetailModal
      v-model:visible="projectDetailModal.visible"
      :project="projectDetailModal.project"
      @edit-project="handleEditFromDetail"
    /> -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useConfigStore } from '@/stores/config'
import { useToast } from 'primevue/usetoast'
// import { FilterMatchMode } from 'primevue/api'
import { format } from 'date-fns'
import VerificationModal from '@/components/VerificationModal.vue'
import ProjectForm from '@/components/ProjectForm.vue'
import CollaboratorDialog from '@/components/CollaboratorDialog.vue'
import ProjectDetailModal from '@/components/ProjectDetailModal.vue'
import Tag from 'primevue/tag'

const router = useRouter()
const projectsStore = useProjectsStore()
const configStore = useConfigStore()
const toast = useToast()

// Reactive data
const filters = ref({
  department: null,
  status: null,
  type: null,
})

const tableFilters = ref({
  global: { value: null, matchMode: 'contains' },
})

const verificationModal = ref({
  visible: false,
  projectId: '',
  action: '',
  title: '',
  message: '',
  data: null,
})

const editDialog = ref({
  visible: false,
  project: null,
  loading: false,
})

const collaboratorDialog = ref({
  visible: false,
  mode: 'add',
  project: null,
  loading: false,
})

const projectDetailModal = ref({
  visible: false,
  project: null,
})

// Computed properties
const departmentOptions = computed(() => [
  { label: 'All Departments', value: null },
  ...configStore.departments.map((dept) => ({ label: dept, value: dept })),
])

const statusOptions = computed(() => [
  { label: 'All Statuses', value: null },
  ...configStore.progressStatuses.map((status) => ({ label: status, value: status })),
])

const typeOptions = computed(() => [
  { label: 'All Types', value: null },
  ...configStore.projectTypes.map((type) => ({ label: type, value: type })),
])

const filteredProjects = computed(() => {
  let projects = [...projectsStore.projects]

  if (filters.value.department) {
    projects = projects.filter((p) => p.department === filters.value.department)
  }

  if (filters.value.status) {
    projects = projects.filter((p) => p.progressStatus === filters.value.status)
  }

  if (filters.value.type) {
    projects = projects.filter((p) => p.projectType === filters.value.type)
  }

  return projects
})

// Methods
onMounted(async () => {
  await refreshProjects()
})

async function refreshProjects() {
  await projectsStore.fetchProjects()
}

function applyFilters() {
  // Filters are applied automatically via computed property
}

function getStatusClass(status) {
  switch (status) {
    case 'Completed':
      return 'status-completed'
    case 'In Progress':
      return 'status-in-progress'
    case 'Not Started':
      return 'status-not-started'
    default:
      return 'status-not-started'
  }
}

function formatDate(date) {
  if (!date) return 'N/A'
  try {
    let dateObj
    if (date.toDate && typeof date.toDate === 'function') {
      // Firebase Timestamp
      dateObj = date.toDate()
    } else if (date.seconds) {
      // Firebase Timestamp object
      dateObj = new Date(date.seconds * 1000)
    } else {
      // Regular date
      dateObj = new Date(date)
    }

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return 'Invalid date'
    }

    return format(dateObj, 'MMM dd, yyyy')
  } catch (error) {
    console.warn('Date formatting error:', error, date)
    return 'Invalid date'
  }
}

function showProjectDetail(project) {
  // Use the original project object - the issue was elsewhere
  projectDetailModal.value = {
    visible: true,
    project: project,
  }
}

function handleEditFromDetail(project) {
  // Close the detail modal first
  projectDetailModal.value.visible = false

  // Then show the edit verification modal
  editProject(project)
}

function showVerificationModal(projectId, action, title, message, data = null) {
  verificationModal.value = {
    visible: true,
    projectId,
    action,
    title,
    message,
    data,
  }
}

function editProject(project) {
  showVerificationModal(
    project.id,
    'edit',
    'Verify Project Leader - Edit Project',
    "Please enter the project leader's employee number to edit this project.",
    project,
  )
}

function addCollaboratorToProject(project) {
  showVerificationModal(
    project.id,
    'addCollaborator',
    'Verify Project Leader - Add Collaborator',
    "Please enter the project leader's employee number to add a collaborator.",
    project,
  )
}

function removeCollaboratorFromProject(project) {
  showVerificationModal(
    project.id,
    'removeCollaborator',
    'Verify Project Leader - Remove Collaborator',
    "Please enter the project leader's employee number to remove a collaborator.",
    project,
  )
}

function transferLeadership(project) {
  showVerificationModal(
    project.id,
    'transferLeadership',
    'Verify Project Leader - Transfer Leadership',
    "Please enter the current project leader's employee number to transfer leadership.",
    project,
  )
}

// Create a non-reactive function to handle modal transitions
function handleVerificationSuccess(employeeNumber) {
  const action = verificationModal.value.action
  const data = verificationModal.value.data

  // Immediately close verification modal
  verificationModal.value = {
    visible: false,
    projectId: '',
    action: '',
    title: '',
    message: '',
    data: null,
  }

  // Handle the action based on type
  if (action === 'edit') {
    // For edit, directly open the edit dialog
    editDialog.value = {
      visible: true,
      project: data,
      loading: false,
    }
  } else if (action === 'addCollaborator') {
    collaboratorDialog.value = {
      visible: true,
      mode: 'add',
      project: data,
      loading: false,
    }
  } else if (action === 'removeCollaborator') {
    collaboratorDialog.value = {
      visible: true,
      mode: 'remove',
      project: data,
      loading: false,
    }
  } else if (action === 'transferLeadership') {
    collaboratorDialog.value = {
      visible: true,
      mode: 'transfer',
      project: data,
      loading: false,
    }
  }
}

function handleVerificationCancel() {
  // Reset verification modal
  verificationModal.value = {
    visible: false,
    projectId: '',
    action: '',
    title: '',
    message: '',
    data: null,
  }
}

async function handleEditSubmit(formData) {
  editDialog.value.loading = true

  try {
    await projectsStore.updateProject(editDialog.value.project.id, formData)

    toast.add({
      severity: 'success',
      summary: 'Project Updated',
      detail: 'Project has been successfully updated',
      life: 3000,
    })

    editDialog.value.visible = false
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Update Failed',
      detail: error.message || 'Failed to update project',
      life: 5000,
    })
  } finally {
    editDialog.value.loading = false
  }
}

async function handleCollaboratorAction({ mode, data }) {
  collaboratorDialog.value.loading = true

  try {
    const project = collaboratorDialog.value.project
    let updateData = {}

    switch (mode) {
      case 'add':
        // Add new collaborator to the project
        const currentCollaborators = project.collaborators || []
        updateData = {
          collaborators: [...currentCollaborators, data],
        }
        break

      case 'remove':
        // Remove collaborator from the project
        const filteredCollaborators = project.collaborators.filter(
          (_, index) => index !== data.collaboratorIndex,
        )
        updateData = {
          collaborators: filteredCollaborators,
        }
        break

      case 'transfer':
        // Transfer leadership - move current leader to collaborators and set new leader
        const currentCollaborators2 = project.collaborators || []
        const currentLeader = project.projectLeader

        updateData = {
          projectLeader: data,
          collaborators: [...currentCollaborators2, currentLeader],
        }
        break
    }

    await projectsStore.updateProject(project.id, updateData)

    const actionMessages = {
      add: 'Collaborator added successfully',
      remove: 'Collaborator removed successfully',
      transfer: 'Leadership transferred successfully',
    }

    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: actionMessages[mode],
      life: 3000,
    })

    collaboratorDialog.value.visible = false
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Action Failed',
      detail: error.message || `Failed to ${mode} collaborator`,
      life: 5000,
    })
  } finally {
    collaboratorDialog.value.loading = false
  }
}
</script>

<style scoped>
.project-title-cell {
  transition: all 0.2s ease;
  padding: 0.5rem;
  border-radius: 0.375rem;
}

.project-title-cell:hover {
  background-color: var(--p-primary-50);
  transform: translateY(-1px);
}

.cursor-pointer {
  cursor: pointer;
}

.transition-colors {
  transition: color 0.2s ease;
}

.hover\:text-primary-600:hover {
  color: var(--p-primary-600);
}

.text-primary {
  color: var(--p-primary-color);
}
</style>
