<template>
  <div>
    <!-- Header -->
    <div
      class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0"
    >
      <div>
        <h1 class="text-2xl md:text-3xl font-bold text-secondary-800 mb-2">Project Registry</h1>
        <p class="text-secondary-600">Manage and view all registered EBP projects</p>
      </div>

      <div class="flex space-x-2">
        <Button
          @click="refreshProjects"
          icon="pi pi-refresh"
          label="Refresh"
          class="p-button-outlined"
          :loading="projectsStore.loading"
        />
        <Button @click="$router.push('/register')" icon="pi pi-plus" label="New Project" />
      </div>
    </div>

    <!-- Filters -->
    <Card class="mb-6">
      <template #content>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="form-label">Filter by Department</label>
            <Dropdown
              v-model="filters.department"
              :options="departmentOptions"
              placeholder="All Departments"
              @change="applyFilters"
            />
          </div>

          <div>
            <label class="form-label">Filter by Status</label>
            <Dropdown
              v-model="filters.status"
              :options="statusOptions"
              placeholder="All Statuses"
              @change="applyFilters"
            />
          </div>

          <div>
            <label class="form-label">Filter by Type</label>
            <Dropdown
              v-model="filters.type"
              :options="typeOptions"
              placeholder="All Types"
              @change="applyFilters"
            />
          </div>
        </div>
      </template>
    </Card>

    <!-- Projects Table -->
    <Card>
      <template #content>
        <DataTable
          :value="filteredProjects"
          :paginator="true"
          :rows="20"
          :loading="projectsStore.loading"
          responsiveLayout="scroll"
          :globalFilterFields="['projectTitle', 'department', 'projectLeader.fullName']"
          v-model:filters="tableFilters"
          filterDisplay="menu"
          :sortField="'createdAt'"
          :sortOrder="-1"
        >
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg font-semibold text-secondary-800">
                Projects ({{ filteredProjects.length }})
              </span>
              <span class="p-input-icon-left">
                <i class="pi pi-search text-secondary-500" />
                <InputText
                  v-model="tableFilters['global'].value"
                  placeholder="Search projects..."
                />
              </span>
            </div>
          </template>

          <Column field="projectTitle" header="Project Title" sortable>
            <template #body="{ data }">
              <div class="font-medium text-secondary-800">{{ data.projectTitle }}</div>
              <div class="text-sm text-secondary-600">ID: {{ data.id }}</div>
            </template>
          </Column>

          <Column field="department" header="Department" sortable />

          <Column field="projectLeader.fullName" header="Project Leader" sortable>
            <template #body="{ data }">
              <div class="font-medium text-secondary-800">{{ data.projectLeader.fullName }}</div>
              <div class="text-sm text-secondary-600">{{ data.projectLeader.rank }}</div>
            </template>
          </Column>

          <Column field="progressStatus" header="Status" sortable>
            <template #body="{ data }">
              <span
                class="px-2 py-1 rounded-full text-xs font-medium"
                :class="getStatusClass(data.progressStatus)"
              >
                {{ data.progressStatus }}
              </span>
            </template>
          </Column>

          <Column field="projectType" header="Type" sortable />

          <Column field="commencementDate" header="Start Date" sortable>
            <template #body="{ data }">
              {{ formatDate(data.commencementDate) }}
            </template>
          </Column>

          <Column header="Actions" :exportable="false">
            <template #body="{ data }">
              <div class="flex space-x-1">
                <Button
                  @click="editProject(data)"
                  icon="pi pi-pencil"
                  class="p-button-sm p-button-text"
                  v-tooltip="'Edit Project'"
                />
                <Button
                  @click="addCollaboratorToProject(data)"
                  icon="pi pi-user-plus"
                  class="p-button-sm p-button-text"
                  v-tooltip="'Add Collaborator'"
                />
                <Button
                  @click="removeCollaboratorFromProject(data)"
                  icon="pi pi-user-minus"
                  class="p-button-sm p-button-text"
                  v-tooltip="'Remove Collaborator'"
                />
                <Button
                  @click="transferLeadership(data)"
                  icon="pi pi-users"
                  class="p-button-sm p-button-text"
                  v-tooltip="'Transfer Leadership'"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </template>
    </Card>

    <!-- Verification Modal -->
    <VerificationModal
      :visible="verificationModal.visible"
      @update:visible="verificationModal.visible = $event"
      :project-id="verificationModal.projectId"
      :title="verificationModal.title"
      :message="verificationModal.message"
      @verified="handleVerificationSuccess"
      @cancelled="handleVerificationCancel"
    />

    <!-- Edit Project Dialog -->
    <Dialog
      :visible="editDialog.visible"
      @update:visible="editDialog.visible = $event"
      modal
      header="Edit Project"
      :style="{ width: '90vw', maxWidth: '800px' }"
    >
      <ProjectForm
        v-if="editDialog.visible"
        :initial-data="editDialog.project"
        :is-edit="true"
        :loading="editDialog.loading"
        @submit="handleEditSubmit"
        @cancel="editDialog.visible = false"
      />
    </Dialog>

    <!-- Collaborator Management Dialog -->
    <CollaboratorDialog
      :visible="collaboratorDialog.visible"
      @update:visible="collaboratorDialog.visible = $event"
      :mode="collaboratorDialog.mode"
      :project="collaboratorDialog.project"
      :loading="collaboratorDialog.loading"
      @action="handleCollaboratorAction"
      @cancel="collaboratorDialog.visible = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useConfigStore } from '@/stores/config'
import { useToast } from 'primevue/usetoast'
// import { FilterMatchMode } from 'primevue/api'
import { format } from 'date-fns'
import VerificationModal from '@/components/VerificationModal.vue'
import ProjectForm from '@/components/ProjectForm.vue'
import CollaboratorDialog from '@/components/CollaboratorDialog.vue'

const router = useRouter()
const projectsStore = useProjectsStore()
const configStore = useConfigStore()
const toast = useToast()

// Reactive data
const filters = ref({
  department: null,
  status: null,
  type: null,
})

const tableFilters = ref({
  global: { value: null, matchMode: 'contains' },
})

const verificationModal = ref({
  visible: false,
  projectId: '',
  action: '',
  title: '',
  message: '',
  data: null,
})

const editDialog = ref({
  visible: false,
  project: null,
  loading: false,
})

const collaboratorDialog = ref({
  visible: false,
  mode: 'add',
  project: null,
  loading: false,
})

// Computed properties
const departmentOptions = computed(() => [
  { label: 'All Departments', value: null },
  ...configStore.departments.map((dept) => ({ label: dept, value: dept })),
])

const statusOptions = computed(() => [
  { label: 'All Statuses', value: null },
  ...configStore.progressStatuses.map((status) => ({ label: status, value: status })),
])

const typeOptions = computed(() => [
  { label: 'All Types', value: null },
  ...configStore.projectTypes.map((type) => ({ label: type, value: type })),
])

const filteredProjects = computed(() => {
  let projects = [...projectsStore.projects]

  if (filters.value.department) {
    projects = projects.filter((p) => p.department === filters.value.department)
  }

  if (filters.value.status) {
    projects = projects.filter((p) => p.progressStatus === filters.value.status)
  }

  if (filters.value.type) {
    projects = projects.filter((p) => p.projectType === filters.value.type)
  }

  return projects
})

// Methods
onMounted(async () => {
  await refreshProjects()
})

async function refreshProjects() {
  await projectsStore.fetchProjects()
}

function applyFilters() {
  // Filters are applied automatically via computed property
}

function getStatusClass(status) {
  switch (status) {
    case 'Completed':
      return 'bg-success-100 text-success-900 border border-success-300'
    case 'In Progress':
      return 'bg-primary-100 text-primary-900 border border-primary-300'
    case 'Not Started':
      return 'bg-secondary-100 text-secondary-800 border border-secondary-300'
    default:
      return 'bg-secondary-100 text-secondary-800 border border-secondary-300'
  }
}

function formatDate(date) {
  if (!date) return 'N/A'
  const dateObj = date.toDate ? date.toDate() : new Date(date)
  return format(dateObj, 'MMM dd, yyyy')
}

function showVerificationModal(projectId, action, title, message, data = null) {
  verificationModal.value = {
    visible: true,
    projectId,
    action,
    title,
    message,
    data,
  }
}

function editProject(project) {
  showVerificationModal(
    project.id,
    'edit',
    'Verify Project Leader - Edit Project',
    "Please enter the project leader's employee number to edit this project.",
    project,
  )
}

function addCollaboratorToProject(project) {
  showVerificationModal(
    project.id,
    'addCollaborator',
    'Verify Project Leader - Add Collaborator',
    "Please enter the project leader's employee number to add a collaborator.",
    project,
  )
}

function removeCollaboratorFromProject(project) {
  showVerificationModal(
    project.id,
    'removeCollaborator',
    'Verify Project Leader - Remove Collaborator',
    "Please enter the project leader's employee number to remove a collaborator.",
    project,
  )
}

function transferLeadership(project) {
  showVerificationModal(
    project.id,
    'transferLeadership',
    'Verify Project Leader - Transfer Leadership',
    "Please enter the current project leader's employee number to transfer leadership.",
    project,
  )
}

function handleVerificationSuccess(employeeNumber) {
  const { action, data } = verificationModal.value

  switch (action) {
    case 'edit':
      editDialog.value = {
        visible: true,
        project: data,
        loading: false,
      }
      break
    case 'addCollaborator':
      collaboratorDialog.value = {
        visible: true,
        mode: 'add',
        project: data,
        loading: false,
      }
      break
    case 'removeCollaborator':
      collaboratorDialog.value = {
        visible: true,
        mode: 'remove',
        project: data,
        loading: false,
      }
      break
    case 'transferLeadership':
      collaboratorDialog.value = {
        visible: true,
        mode: 'transfer',
        project: data,
        loading: false,
      }
      break
  }
}

function handleVerificationCancel() {
  // Reset verification modal
  verificationModal.value = {
    visible: false,
    projectId: '',
    action: '',
    title: '',
    message: '',
    data: null,
  }
}

async function handleEditSubmit(formData) {
  editDialog.value.loading = true

  try {
    await projectsStore.updateProject(editDialog.value.project.id, formData)

    toast.add({
      severity: 'success',
      summary: 'Project Updated',
      detail: 'Project has been successfully updated',
      life: 3000,
    })

    editDialog.value.visible = false
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Update Failed',
      detail: error.message || 'Failed to update project',
      life: 5000,
    })
  } finally {
    editDialog.value.loading = false
  }
}

async function handleCollaboratorAction({ mode, data }) {
  collaboratorDialog.value.loading = true

  try {
    const project = collaboratorDialog.value.project
    let updateData = {}

    switch (mode) {
      case 'add':
        // Add new collaborator to the project
        const currentCollaborators = project.collaborators || []
        updateData = {
          collaborators: [...currentCollaborators, data],
        }
        break

      case 'remove':
        // Remove collaborator from the project
        const filteredCollaborators = project.collaborators.filter(
          (_, index) => index !== data.collaboratorIndex,
        )
        updateData = {
          collaborators: filteredCollaborators,
        }
        break

      case 'transfer':
        // Transfer leadership - move current leader to collaborators and set new leader
        const currentCollaborators2 = project.collaborators || []
        const currentLeader = project.projectLeader

        updateData = {
          projectLeader: data,
          collaborators: [...currentCollaborators2, currentLeader],
        }
        break
    }

    await projectsStore.updateProject(project.id, updateData)

    const actionMessages = {
      add: 'Collaborator added successfully',
      remove: 'Collaborator removed successfully',
      transfer: 'Leadership transferred successfully',
    }

    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: actionMessages[mode],
      life: 3000,
    })

    collaboratorDialog.value.visible = false
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Action Failed',
      detail: error.message || `Failed to ${mode} collaborator`,
      life: 5000,
    })
  } finally {
    collaboratorDialog.value.loading = false
  }
}
</script>
