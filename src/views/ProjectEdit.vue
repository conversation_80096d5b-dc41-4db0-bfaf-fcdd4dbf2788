<template>
  <div class="project-edit-page">
    <!-- Header -->
    <div class="page-header mb-4">
      <div class="flex justify-content-between align-items-center">
        <div>
          <h1 class="text-3xl font-bold text-color mb-2">Edit Project</h1>
          <p class="text-color-secondary">
            {{ project?.projectTitle || 'Loading...' }}
          </p>
        </div>
        <Button
          label="Back to Projects"
          icon="pi pi-arrow-left"
          text
          @click="goBack"
        />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-content-center align-items-center" style="height: 400px;">
      <ProgressSpinner />
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center">
      <Message severity="error" :closable="false">
        {{ error }}
      </Message>
      <Button
        label="Back to Projects"
        icon="pi pi-arrow-left"
        class="mt-3"
        @click="goBack"
      />
    </div>

    <!-- Edit Form -->
    <Card v-else-if="project" class="w-full">
      <template #content>
        <ProjectForm
          :initial-data="project"
          :is-edit="true"
          @submit="handleSubmit"
          @cancel="handleCancel"
        />
      </template>
    </Card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useToast } from 'primevue/usetoast'
import ProjectForm from '@/components/ProjectForm.vue'

const route = useRoute()
const router = useRouter()
const projectsStore = useProjectsStore()
const toast = useToast()

const project = ref(null)
const loading = ref(true)
const error = ref('')

onMounted(async () => {
  try {
    // First try to get project data from sessionStorage
    const storedData = sessionStorage.getItem('editProjectData')
    if (storedData) {
      project.value = JSON.parse(storedData)
      sessionStorage.removeItem('editProjectData')
      loading.value = false
      return
    }

    // If no stored data, fetch from store
    const projectId = route.params.id
    await projectsStore.fetchProjects()
    
    const foundProject = projectsStore.projects.find(p => p.id === projectId)
    if (foundProject) {
      project.value = foundProject
    } else {
      error.value = 'Project not found'
    }
  } catch (err) {
    error.value = 'Failed to load project data'
    console.error('Error loading project:', err)
  } finally {
    loading.value = false
  }
})

async function handleSubmit(formData) {
  try {
    await projectsStore.updateProject(project.value.id, formData)
    
    toast.add({
      severity: 'success',
      summary: 'Project Updated',
      detail: 'Project has been successfully updated',
      life: 3000,
    })

    // Navigate back to projects list
    router.push('/projects')
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Update Failed',
      detail: error.message || 'Failed to update project',
      life: 5000,
    })
  }
}

function handleCancel() {
  goBack()
}

function goBack() {
  router.push('/projects')
}
</script>

<style scoped>
.project-edit-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  border-bottom: 1px solid var(--p-surface-200);
  padding-bottom: 1rem;
}

@media (max-width: 768px) {
  .project-edit-page {
    padding: 1rem;
  }
  
  .page-header .flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
</style>
