<script setup>
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useConfigStore } from '@/stores/config'
import AppNavigation from '@/components/AppNavigation.vue'
// import DevTools from '@/components/DevTools.vue'

const authStore = useAuthStore()
const configStore = useConfigStore()

onMounted(async () => {
  // Initialize authentication state
  await authStore.initializeAuth()

  // Load configuration
  await configStore.loadConfig()
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-secondary-50 to-primary-50">
    <AppNavigation />

    <main class="container mx-auto px-4 py-8">
      <RouterView />
    </main>

    <!-- Global Toast for notifications -->
    <Toast />

    <!-- Global Confirm Dialog -->
    <ConfirmDialog />

    <!-- Development Tools -->
    <!-- <DevTools /> -->
  </div>
</template>

<style>
/* Global styles */
.p-button {
  font-weight: 500;
}

.p-inputtext {
  width: 100%;
}

.p-dropdown {
  width: 100%;
}

.p-calendar {
  width: 100%;
}

.p-textarea {
  width: 100%;
}

/* Responsive table styles */
.p-datatable .p-datatable-tbody > tr > td {
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .p-datatable .p-datatable-tbody > tr > td {
    font-size: 0.75rem;
  }
}

/* Custom form styles */
.form-field {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.5rem;
}

.form-error {
  color: #dc2626;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Loading spinner */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border: 2px solid #e2e8f0;
  border-bottom: 2px solid #0284c7;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* PrimeVue theme color overrides */
:root {
  --primary-color: #0369a1 !important;
  --primary-color-text: #ffffff !important;
  --surface-0: #ffffff !important;
  --surface-50: #f8fafc !important;
  --surface-100: #f1f5f9 !important;
  --surface-200: #e2e8f0 !important;
  --surface-300: #cbd5e1 !important;
  --surface-400: #94a3b8 !important;
  --surface-500: #64748b !important;
  --surface-600: #475569 !important;
  --surface-700: #334155 !important;
  --surface-800: #1e293b !important;
  --surface-900: #0f172a !important;
  --text-color: #334155 !important;
  --text-color-secondary: #64748b !important;
}

/* PrimeVue component overrides for better contrast */
.p-button.p-button-text {
  color: #334155 !important;
}

.p-button.p-button-text:hover {
  color: #0369a1 !important;
  background-color: #f0f9ff !important;
}

.p-button.p-button-outlined {
  color: #0369a1 !important;
  border-color: #0369a1 !important;
}

.p-button.p-button-outlined:hover {
  background-color: #f0f9ff !important;
  border-color: #0369a1 !important;
}

/* DataTable header text */
.p-datatable .p-datatable-header {
  color: #334155 !important;
}

/* Dropdown and input labels */
.p-dropdown-label {
  color: #334155 !important;
}

/* Table cell text */
.p-datatable .p-datatable-tbody > tr > td {
  color: #334155 !important;
}

/* Status badge improvements */
.p-tag {
  font-weight: 600 !important;
}

/* Filter labels and form labels */
label.form-label {
  color: #1e293b !important;
  font-weight: 600 !important;
}

/* Button text improvements */
.p-button .p-button-label {
  color: inherit !important;
}

/* Dropdown placeholder and selected text */
.p-dropdown .p-dropdown-label {
  color: #1e293b !important;
  font-weight: 500 !important;
}

.p-dropdown .p-dropdown-label.p-placeholder {
  color: #475569 !important;
  font-weight: 400 !important;
}

/* Dropdown panel and options */
.p-dropdown-panel {
  border: 1px solid #cbd5e1 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.p-dropdown-panel .p-dropdown-items {
  padding: 0.5rem 0 !important;
}

.p-dropdown-panel .p-dropdown-item {
  color: #334155 !important;
  padding: 0.75rem 1rem !important;
  font-weight: 500 !important;
}

.p-dropdown-panel .p-dropdown-item:hover {
  background-color: #f1f5f9 !important;
  color: #0369a1 !important;
}

.p-dropdown-panel .p-dropdown-item.p-highlight {
  background-color: #e0f2fe !important;
  color: #0369a1 !important;
}

/* Dropdown trigger button */
.p-dropdown .p-dropdown-trigger {
  color: #475569 !important;
}

.p-dropdown:hover .p-dropdown-trigger {
  color: #334155 !important;
}

/* Dropdown border and focus states */
.p-dropdown {
  border-color: #cbd5e1 !important;
}

.p-dropdown:not(.p-disabled):hover {
  border-color: #94a3b8 !important;
}

.p-dropdown:not(.p-disabled).p-focus {
  border-color: #0369a1 !important;
  box-shadow: 0 0 0 2px rgba(3, 105, 161, 0.2) !important;
}

/* Navigation button overrides */
.p-button.p-button-text.p-button-sm {
  color: #334155 !important;
  font-weight: 500 !important;
}

.p-button.p-button-text.p-button-sm:hover {
  color: #0369a1 !important;
  background-color: #f0f9ff !important;
}

/* Mobile menu button */
.p-button.p-button-text.md\:hidden {
  color: #334155 !important;
}

.p-button.p-button-text.md\:hidden:hover {
  color: #0369a1 !important;
  background-color: #f0f9ff !important;
}

/* Primary buttons */
.p-button:not(.p-button-outlined):not(.p-button-text) {
  background-color: #0369a1 !important;
  border-color: #0369a1 !important;
  color: white !important;
}

.p-button:not(.p-button-outlined):not(.p-button-text):hover {
  background-color: #075985 !important;
  border-color: #075985 !important;
}

/* Action buttons in table */
.p-button.p-button-sm.p-button-text {
  color: #475569 !important;
}

.p-button.p-button-sm.p-button-text:hover {
  color: #0369a1 !important;
  background-color: #f1f5f9 !important;
}
</style>
