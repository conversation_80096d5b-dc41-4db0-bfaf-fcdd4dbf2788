<script setup>
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useConfigStore } from '@/stores/config'
import AppNavigation from '@/components/AppNavigation.vue'
// import FirebaseStatus from '@/components/FirebaseStatus.vue'
// import DevTools from '@/components/DevTools.vue'

const authStore = useAuthStore()
const configStore = useConfigStore()

onMounted(async () => {
  // Initialize authentication state
  await authStore.initializeAuth()

  // Load configuration
  await configStore.loadConfig()
})
</script>

<template>
  <div class="app-container">
    <AppNavigation />

    <main class="container p-6">
      <RouterView />
    </main>

    <!-- Global Toast for notifications -->
    <Toast />

    <!-- Global Confirm Dialog -->
    <ConfirmDialog />

    <!-- Development Tools -->
    <!-- <DevTools /> -->

    <!-- Firebase Status (Development Only) -->
    <!-- <FirebaseStatus /> -->
  </div>
</template>

<style>
/* App Layout */
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--p-surface-50), var(--p-primary-50));
}

/* Global PrimeVue Component Styles */
.p-button {
  font-weight: 500;
}

.p-inputtext {
  width: 100%;
}

.p-select {
  width: 100%;
}

.p-datepicker {
  width: 100%;
}

.p-textarea {
  width: 100%;
}

/* Responsive table styles */
.p-datatable .p-datatable-tbody > tr > td {
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .p-datatable .p-datatable-tbody > tr > td {
    font-size: 0.75rem;
  }
}

/* Form styles using PrimeVue variables */
.form-field {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--p-text-color);
  margin-bottom: 0.5rem;
}

.form-error {
  color: var(--p-red-500);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Loading spinner using PrimeVue variables */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border: 2px solid var(--p-surface-200);
  border-bottom: 2px solid var(--p-primary-color);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* PrimeVue Component Enhancements */
.p-button.p-button-text {
  color: var(--p-text-color);
}

.p-button.p-button-text:hover {
  color: var(--p-primary-color);
  background-color: var(--p-primary-50);
}

.p-button.p-button-outlined {
  color: var(--p-primary-color);
  border-color: var(--p-primary-color);
}

.p-button.p-button-outlined:hover {
  background-color: var(--p-primary-50);
  border-color: var(--p-primary-color);
}

/* DataTable styling */
.p-datatable .p-datatable-header {
  color: var(--p-text-color);
}

.p-datatable .p-datatable-tbody > tr > td {
  color: var(--p-text-color);
}

/* Select styling */
.p-select-label {
  color: var(--p-text-color);
}

.p-dropdown .p-dropdown-label {
  color: var(--p-text-color);
  font-weight: 500;
}

.p-dropdown .p-dropdown-label.p-placeholder {
  color: var(--p-text-muted-color);
  font-weight: 400;
}

/* Status badges */
.p-tag {
  font-weight: 600;
}

/* Form labels */
label.form-label {
  color: var(--p-text-color);
  font-weight: 600;
}

/* Enhanced Dropdown Styling */
.p-dropdown-panel {
  border: 1px solid var(--p-surface-border);
  box-shadow: var(--p-shadow-4);
}

.p-dropdown-panel .p-dropdown-items {
  padding: 0.5rem 0;
}

.p-dropdown-panel .p-dropdown-item {
  color: var(--p-text-color);
  padding: 0.75rem 1rem;
  font-weight: 500;
}

.p-dropdown-panel .p-dropdown-item:hover {
  background-color: var(--p-primary-50);
  color: var(--p-primary-color);
}

.p-dropdown-panel .p-dropdown-item.p-highlight {
  background-color: var(--p-primary-100);
  color: var(--p-primary-color);
}

/* Dropdown trigger styling */
.p-dropdown .p-dropdown-trigger {
  color: var(--p-text-muted-color);
}

.p-dropdown:hover .p-dropdown-trigger {
  color: var(--p-text-color);
}

/* Dropdown focus states */
.p-dropdown {
  border-color: var(--p-surface-border);
}

.p-dropdown:not(.p-disabled):hover {
  border-color: var(--p-surface-400);
}

.p-dropdown:not(.p-disabled).p-focus {
  border-color: var(--p-primary-color);
  box-shadow: 0 0 0 2px var(--p-primary-100);
}
</style>
