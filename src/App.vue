<script setup>
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useConfigStore } from '@/stores/config'
import AppNavigation from '@/components/AppNavigation.vue'
// import DevTools from '@/components/DevTools.vue'

const authStore = useAuthStore()
const configStore = useConfigStore()

onMounted(async () => {
  // Initialize authentication state
  await authStore.initializeAuth()

  // Load configuration
  await configStore.loadConfig()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <AppNavigation />

    <main class="container mx-auto px-4 py-6">
      <RouterView />
    </main>

    <!-- Global Toast for notifications -->
    <Toast />

    <!-- Global Confirm Dialog -->
    <ConfirmDialog />

    <!-- Development Tools -->
    <!-- <DevTools /> -->
  </div>
</template>

<style>
/* Global styles */
.p-button {
  font-weight: 500;
}

.p-inputtext {
  width: 100%;
}

.p-dropdown {
  width: 100%;
}

.p-calendar {
  width: 100%;
}

.p-textarea {
  width: 100%;
}

/* Responsive table styles */
.p-datatable .p-datatable-tbody > tr > td {
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .p-datatable .p-datatable-tbody > tr > td {
    font-size: 0.75rem;
  }
}

/* Custom form styles */
.form-field {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.form-error {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Loading spinner */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border-bottom: 2px solid #2563eb;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
