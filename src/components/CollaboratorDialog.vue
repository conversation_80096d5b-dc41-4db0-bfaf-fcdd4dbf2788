<template>
  <Dialog 
    v-model:visible="visible" 
    modal 
    :header="dialogTitle"
    :style="{ width: '90vw', maxWidth: '600px' }"
  >
    <div class="space-y-4">
      <!-- Add Collaborator Mode -->
      <div v-if="mode === 'add'" class="space-y-4">
        <p class="text-gray-600">
          Add a new collaborator to the project "{{ project?.projectTitle }}"
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Rank -->
          <div class="form-field">
            <label class="form-label">Rank *</label>
            <Dropdown
              v-model="newCollaborator.rank"
              :options="configStore.ranks"
              placeholder="Select rank"
              :class="{ 'p-invalid': errors.rank }"
            />
            <small v-if="errors.rank" class="form-error">
              {{ errors.rank }}
            </small>
          </div>

          <!-- Full Name -->
          <div class="form-field">
            <label class="form-label">Full Name *</label>
            <InputText
              v-model="newCollaborator.fullName"
              placeholder="Enter full name"
              :class="{ 'p-invalid': errors.fullName }"
            />
            <small v-if="errors.fullName" class="form-error">
              {{ errors.fullName }}
            </small>
          </div>

          <!-- Employee Number -->
          <div class="form-field">
            <label class="form-label">Employee Number *</label>
            <InputText
              v-model="newCollaborator.employeeNumber"
              placeholder="6-digit number"
              maxlength="6"
              :class="{ 'p-invalid': errors.employeeNumber }"
            />
            <small v-if="errors.employeeNumber" class="form-error">
              {{ errors.employeeNumber }}
            </small>
          </div>
        </div>
      </div>

      <!-- Remove Collaborator Mode -->
      <div v-else-if="mode === 'remove'" class="space-y-4">
        <p class="text-gray-600">
          Select a collaborator to remove from "{{ project?.projectTitle }}"
        </p>
        
        <div v-if="project?.collaborators?.length > 0">
          <div class="form-field">
            <label class="form-label">Select Collaborator *</label>
            <Dropdown
              v-model="selectedCollaborator"
              :options="collaboratorOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="Choose collaborator to remove"
              :class="{ 'p-invalid': errors.selectedCollaborator }"
            />
            <small v-if="errors.selectedCollaborator" class="form-error">
              {{ errors.selectedCollaborator }}
            </small>
          </div>
          
          <div v-if="selectedCollaborator" class="p-4 bg-red-50 border border-red-200 rounded-md">
            <div class="flex">
              <i class="pi pi-exclamation-triangle text-red-400 mr-2"></i>
              <div class="text-sm text-red-700">
                <strong>Warning:</strong> This will permanently remove the selected collaborator from the project.
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="text-center py-8 text-gray-500">
          <i class="pi pi-users text-4xl mb-4 block"></i>
          <p>No collaborators found for this project.</p>
        </div>
      </div>

      <!-- Transfer Leadership Mode -->
      <div v-else-if="mode === 'transfer'" class="space-y-4">
        <p class="text-gray-600">
          Transfer leadership of "{{ project?.projectTitle }}" to a new project leader
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- New Leader Rank -->
          <div class="form-field">
            <label class="form-label">New Leader Rank *</label>
            <Dropdown
              v-model="newLeader.rank"
              :options="configStore.ranks"
              placeholder="Select rank"
              :class="{ 'p-invalid': errors.rank }"
            />
            <small v-if="errors.rank" class="form-error">
              {{ errors.rank }}
            </small>
          </div>

          <!-- New Leader Full Name -->
          <div class="form-field">
            <label class="form-label">New Leader Full Name *</label>
            <InputText
              v-model="newLeader.fullName"
              placeholder="Enter full name"
              :class="{ 'p-invalid': errors.fullName }"
            />
            <small v-if="errors.fullName" class="form-error">
              {{ errors.fullName }}
            </small>
          </div>

          <!-- New Leader Employee Number -->
          <div class="form-field">
            <label class="form-label">New Leader Employee Number *</label>
            <InputText
              v-model="newLeader.employeeNumber"
              placeholder="6-digit number"
              maxlength="6"
              :class="{ 'p-invalid': errors.employeeNumber }"
            />
            <small v-if="errors.employeeNumber" class="form-error">
              {{ errors.employeeNumber }}
            </small>
          </div>
        </div>
        
        <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <div class="flex">
            <i class="pi pi-exclamation-triangle text-yellow-400 mr-2"></i>
            <div class="text-sm text-yellow-700">
              <strong>Note:</strong> The current project leader will be moved to the collaborators list.
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button 
          label="Cancel" 
          icon="pi pi-times" 
          class="p-button-text"
          @click="cancel"
          :disabled="loading"
        />
        <Button 
          :label="actionLabel"
          :icon="actionIcon"
          @click="handleAction"
          :loading="loading"
          :disabled="!canProceed"
          :severity="mode === 'remove' ? 'danger' : 'primary'"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useConfigStore } from '@/stores/config'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    required: true,
    validator: (value) => ['add', 'remove', 'transfer'].includes(value)
  },
  project: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'action', 'cancel'])

const configStore = useConfigStore()

// Form data
const newCollaborator = ref({
  rank: '',
  fullName: '',
  employeeNumber: ''
})

const newLeader = ref({
  rank: '',
  fullName: '',
  employeeNumber: ''
})

const selectedCollaborator = ref(null)
const errors = ref({})

// Computed properties
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add':
      return 'Add Collaborator'
    case 'remove':
      return 'Remove Collaborator'
    case 'transfer':
      return 'Transfer Leadership'
    default:
      return 'Manage Project'
  }
})

const actionLabel = computed(() => {
  switch (props.mode) {
    case 'add':
      return 'Add Collaborator'
    case 'remove':
      return 'Remove Collaborator'
    case 'transfer':
      return 'Transfer Leadership'
    default:
      return 'Proceed'
  }
})

const actionIcon = computed(() => {
  switch (props.mode) {
    case 'add':
      return 'pi pi-user-plus'
    case 'remove':
      return 'pi pi-user-minus'
    case 'transfer':
      return 'pi pi-users'
    default:
      return 'pi pi-check'
  }
})

const collaboratorOptions = computed(() => {
  if (!props.project?.collaborators) return []
  
  return props.project.collaborators.map((collaborator, index) => ({
    label: `${collaborator.fullName} (${collaborator.rank}) - ${collaborator.employeeNumber}`,
    value: index
  }))
})

const canProceed = computed(() => {
  switch (props.mode) {
    case 'add':
      return newCollaborator.value.rank && 
             newCollaborator.value.fullName && 
             newCollaborator.value.employeeNumber &&
             Object.keys(errors.value).length === 0
    case 'remove':
      return selectedCollaborator.value !== null && props.project?.collaborators?.length > 0
    case 'transfer':
      return newLeader.value.rank && 
             newLeader.value.fullName && 
             newLeader.value.employeeNumber &&
             Object.keys(errors.value).length === 0
    default:
      return false
  }
})

// Watchers
watch(() => props.visible, (newValue) => {
  if (newValue) {
    resetForm()
  }
})

// Methods
function resetForm() {
  newCollaborator.value = {
    rank: '',
    fullName: '',
    employeeNumber: ''
  }
  
  newLeader.value = {
    rank: '',
    fullName: '',
    employeeNumber: ''
  }
  
  selectedCollaborator.value = null
  errors.value = {}
}

function validateForm() {
  errors.value = {}
  
  if (props.mode === 'add') {
    if (!newCollaborator.value.rank) {
      errors.value.rank = 'Rank is required'
    }
    if (!newCollaborator.value.fullName) {
      errors.value.fullName = 'Full name is required'
    }
    if (!newCollaborator.value.employeeNumber) {
      errors.value.employeeNumber = 'Employee number is required'
    } else if (!/^\d{6}$/.test(newCollaborator.value.employeeNumber)) {
      errors.value.employeeNumber = 'Employee number must be exactly 6 digits'
    }
  } else if (props.mode === 'remove') {
    if (selectedCollaborator.value === null) {
      errors.value.selectedCollaborator = 'Please select a collaborator to remove'
    }
  } else if (props.mode === 'transfer') {
    if (!newLeader.value.rank) {
      errors.value.rank = 'New leader rank is required'
    }
    if (!newLeader.value.fullName) {
      errors.value.fullName = 'New leader full name is required'
    }
    if (!newLeader.value.employeeNumber) {
      errors.value.employeeNumber = 'New leader employee number is required'
    } else if (!/^\d{6}$/.test(newLeader.value.employeeNumber)) {
      errors.value.employeeNumber = 'Employee number must be exactly 6 digits'
    }
  }
  
  return Object.keys(errors.value).length === 0
}

function handleAction() {
  if (!validateForm()) return
  
  let actionData = {}
  
  switch (props.mode) {
    case 'add':
      actionData = { ...newCollaborator.value }
      break
    case 'remove':
      actionData = { 
        collaboratorIndex: selectedCollaborator.value,
        collaborator: props.project.collaborators[selectedCollaborator.value]
      }
      break
    case 'transfer':
      actionData = { ...newLeader.value }
      break
  }
  
  emit('action', {
    mode: props.mode,
    data: actionData
  })
}

function cancel() {
  emit('cancel')
  emit('update:visible', false)
}
</script>
