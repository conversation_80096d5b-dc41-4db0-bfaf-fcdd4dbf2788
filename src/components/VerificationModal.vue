<template>
  <Dialog
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    modal
    :header="title"
    :style="{ width: '90vw', maxWidth: '400px' }"
    :closable="false"
  >
    <div class="space-y-4">
      <p class="text-gray-600">
        {{ message }}
      </p>

      <div class="form-field">
        <label class="form-label"> Project Leader Employee Number * </label>
        <InputText
          v-model="employeeNumber"
          placeholder="Enter 6-digit employee number"
          :class="{ 'p-invalid': error }"
          maxlength="6"
          @keyup.enter="verify"
        />
        <small v-if="error" class="form-error">
          {{ error }}
        </small>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button
          label="Cancel"
          icon="pi pi-times"
          class="p-button-text"
          @click="cancel"
          :disabled="loading"
        />
        <Button
          label="Verify"
          icon="pi pi-check"
          @click="verify"
          :loading="loading"
          :disabled="!employeeNumber || employeeNumber.length !== 6"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useProjectsStore } from '@/stores/projects'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  projectId: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: 'Verify Project Leader',
  },
  message: {
    type: String,
    default: "Please enter the project leader's employee number to proceed with this action.",
  },
})

const emit = defineEmits(['update:visible', 'verified', 'cancelled'])

const projectsStore = useProjectsStore()
const employeeNumber = ref('')
const error = ref('')
const loading = ref(false)

// Reset form when modal opens/closes
watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      employeeNumber.value = ''
      error.value = ''
    }
  },
)

async function verify() {
  if (!employeeNumber.value || employeeNumber.value.length !== 6) {
    error.value = 'Please enter a valid 6-digit employee number'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const isValid = await projectsStore.verifyProjectLeader(props.projectId, employeeNumber.value)

    if (isValid) {
      emit('verified', employeeNumber.value)
      emit('update:visible', false)
    } else {
      error.value = 'Employee number does not match the project leader'
    }
  } catch (err) {
    error.value = 'Verification failed. Please try again.'
    console.error('Verification error:', err)
  } finally {
    loading.value = false
  }
}

function cancel() {
  emit('cancelled')
  emit('update:visible', false)
}
</script>
