<template>
  <nav class="bg-white shadow-lg">
    <div class="container mx-auto px-4">
      <div class="flex justify-between items-center py-4">
        <!-- Logo/Title -->
        <div class="flex items-center space-x-4">
          <h1 class="text-xl md:text-2xl font-bold text-blue-600">EBP Project Central Registry</h1>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-6">
          <RouterLink
            to="/projects"
            class="nav-link"
            :class="{ active: $route.name === 'projects' }"
          >
            <i class="pi pi-list mr-2"></i>
            Projects
          </RouterLink>

          <RouterLink
            to="/register"
            class="nav-link"
            :class="{ active: $route.name === 'register' }"
          >
            <i class="pi pi-plus mr-2"></i>
            Register Project
          </RouterLink>

          <RouterLink
            v-if="authStore.isAuthenticated"
            to="/admin"
            class="nav-link"
            :class="{ active: $route.name === 'admin' }"
          >
            <i class="pi pi-cog mr-2"></i>
            Admin
          </RouterLink>

          <Button
            v-if="authStore.isAuthenticated"
            @click="handleLogout"
            icon="pi pi-sign-out"
            label="Logout"
            class="p-button-text p-button-sm"
          />

          <RouterLink
            v-else
            to="/login"
            class="nav-link"
            :class="{ active: $route.name === 'login' }"
          >
            <i class="pi pi-sign-in mr-2"></i>
            Admin Login
          </RouterLink>
        </div>

        <!-- Mobile Menu Button -->
        <Button
          @click="mobileMenuOpen = !mobileMenuOpen"
          icon="pi pi-bars"
          class="p-button-text md:hidden"
        />
      </div>

      <!-- Mobile Navigation -->
      <div v-if="mobileMenuOpen" class="md:hidden border-t border-gray-200 py-4">
        <div class="flex flex-col space-y-3">
          <RouterLink to="/projects" class="mobile-nav-link" @click="mobileMenuOpen = false">
            <i class="pi pi-list mr-2"></i>
            Projects
          </RouterLink>

          <RouterLink to="/register" class="mobile-nav-link" @click="mobileMenuOpen = false">
            <i class="pi pi-plus mr-2"></i>
            Register Project
          </RouterLink>

          <RouterLink
            v-if="authStore.isAuthenticated"
            to="/admin"
            class="mobile-nav-link"
            @click="mobileMenuOpen = false"
          >
            <i class="pi pi-cog mr-2"></i>
            Admin
          </RouterLink>

          <button
            v-if="authStore.isAuthenticated"
            @click="handleLogout"
            class="mobile-nav-link text-left"
          >
            <i class="pi pi-sign-out mr-2"></i>
            Logout
          </button>

          <RouterLink v-else to="/login" class="mobile-nav-link" @click="mobileMenuOpen = false">
            <i class="pi pi-sign-in mr-2"></i>
            Admin Login
          </RouterLink>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'primevue/usetoast'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()
const mobileMenuOpen = ref(false)

async function handleLogout() {
  try {
    await authStore.logout()
    toast.add({
      severity: 'success',
      summary: 'Logged Out',
      detail: 'You have been successfully logged out',
      life: 3000,
    })
    router.push('/projects')
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Logout Failed',
      detail: error.message,
      life: 5000,
    })
  }
  mobileMenuOpen.value = false
}
</script>

<style scoped>
.nav-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: #374151;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.nav-link:hover {
  color: #2563eb;
  background-color: #eff6ff;
}

.nav-link.active {
  color: #2563eb;
  background-color: #eff6ff;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: #374151;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.mobile-nav-link:hover {
  color: #2563eb;
  background-color: #eff6ff;
}
</style>
