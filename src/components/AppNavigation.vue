<template>
  <nav class="bg-white shadow-lg border-b border-secondary-200">
    <div class="container mx-auto px-4">
      <div class="flex justify-between items-center py-4">
        <!-- Logo/Title -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-3">
            <div
              class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center"
            >
              <i class="pi pi-heart text-white text-sm"></i>
            </div>
            <h1 class="text-xl md:text-2xl font-bold text-secondary-800">
              EBP Project Central Registry
            </h1>
          </div>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-6">
          <RouterLink
            to="/projects"
            class="nav-link"
            :class="{ active: $route.name === 'projects' }"
          >
            <i class="pi pi-list mr-2"></i>
            Projects
          </RouterLink>

          <RouterLink
            to="/register"
            class="nav-link"
            :class="{ active: $route.name === 'register' }"
          >
            <i class="pi pi-plus mr-2"></i>
            Register Project
          </RouterLink>

          <RouterLink
            v-if="authStore.isAuthenticated"
            to="/admin"
            class="nav-link"
            :class="{ active: $route.name === 'admin' }"
          >
            <i class="pi pi-cog mr-2"></i>
            Admin
          </RouterLink>

          <Button
            v-if="authStore.isAuthenticated"
            @click="handleLogout"
            icon="pi pi-sign-out"
            label="Logout"
            class="p-button-text p-button-sm"
          />

          <RouterLink
            v-else
            to="/login"
            class="nav-link"
            :class="{ active: $route.name === 'login' }"
          >
            <i class="pi pi-sign-in mr-2"></i>
            Admin Login
          </RouterLink>
        </div>

        <!-- Mobile Menu Button -->
        <Button
          @click="mobileMenuOpen = !mobileMenuOpen"
          icon="pi pi-bars"
          class="p-button-text md:hidden"
        />
      </div>

      <!-- Mobile Navigation -->
      <div v-if="mobileMenuOpen" class="md:hidden border-t border-gray-200 py-4">
        <div class="flex flex-col space-y-3">
          <RouterLink to="/projects" class="mobile-nav-link" @click="mobileMenuOpen = false">
            <i class="pi pi-list mr-2"></i>
            Projects
          </RouterLink>

          <RouterLink to="/register" class="mobile-nav-link" @click="mobileMenuOpen = false">
            <i class="pi pi-plus mr-2"></i>
            Register Project
          </RouterLink>

          <RouterLink
            v-if="authStore.isAuthenticated"
            to="/admin"
            class="mobile-nav-link"
            @click="mobileMenuOpen = false"
          >
            <i class="pi pi-cog mr-2"></i>
            Admin
          </RouterLink>

          <button
            v-if="authStore.isAuthenticated"
            @click="handleLogout"
            class="mobile-nav-link text-left"
          >
            <i class="pi pi-sign-out mr-2"></i>
            Logout
          </button>

          <RouterLink v-else to="/login" class="mobile-nav-link" @click="mobileMenuOpen = false">
            <i class="pi pi-sign-in mr-2"></i>
            Admin Login
          </RouterLink>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'primevue/usetoast'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()
const mobileMenuOpen = ref(false)

async function handleLogout() {
  try {
    await authStore.logout()
    toast.add({
      severity: 'success',
      summary: 'Logged Out',
      detail: 'You have been successfully logged out',
      life: 3000,
    })
    router.push('/projects')
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Logout Failed',
      detail: error.message,
      life: 5000,
    })
  }
  mobileMenuOpen.value = false
}
</script>

<style scoped>
.nav-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: #475569;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  font-weight: 500;
}

.nav-link:hover {
  color: #0369a1;
  background-color: #f0f9ff;
  transform: translateY(-1px);
}

.nav-link.active {
  color: #0369a1;
  background-color: #e0f2fe;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #475569;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  font-weight: 500;
  border-left: 3px solid transparent;
}

.mobile-nav-link:hover {
  color: #0369a1;
  background-color: #f0f9ff;
  border-left-color: #0369a1;
}
</style>
