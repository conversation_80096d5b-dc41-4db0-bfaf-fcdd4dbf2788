import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import ToastService from 'primevue/toastservice'
import ConfirmationService from 'primevue/confirmationservice'
import Aura from '@primevue/themes/aura'

// PrimeVue Components
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'
import Calendar from 'primevue/calendar'
import Textarea from 'primevue/textarea'
import RadioButton from 'primevue/radiobutton'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Dialog from 'primevue/dialog'
import Toast from 'primevue/toast'
import ConfirmDialog from 'primevue/confirmdialog'
import Card from 'primevue/card'
import Panel from 'primevue/panel'
import PanelMenu from 'primevue/panelmenu'
import Toolbar from 'primevue/toolbar'
import Paginator from 'primevue/paginator'
import InputNumber from 'primevue/inputnumber'
import Password from 'primevue/password'
import Menubar from 'primevue/menubar'

// PrimeVue Styles
import 'primeicons/primeicons.css'

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      prefix: 'p',
      darkModeSelector: false,
      cssLayer: false,
    },
  },
})
app.use(ToastService)
app.use(ConfirmationService)

// Register PrimeVue components globally
app.component('Button', Button)
app.component('InputText', InputText)
app.component('PrimeSelect', Select)
app.component('Calendar', Calendar)
app.component('Textarea', Textarea)
app.component('RadioButton', RadioButton)
app.component('DataTable', DataTable)
app.component('Column', Column)
app.component('Dialog', Dialog)
app.component('Toast', Toast)
app.component('ConfirmDialog', ConfirmDialog)
app.component('Card', Card)
app.component('Panel', Panel)
app.component('PanelMenu', PanelMenu)
app.component('Toolbar', Toolbar)
app.component('Paginator', Paginator)
app.component('InputNumber', InputNumber)
app.component('Password', Password)
app.component('Menubar', Menubar)

app.mount('#app')
