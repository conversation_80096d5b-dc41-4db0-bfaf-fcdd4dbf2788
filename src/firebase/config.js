import { initializeApp } from 'firebase/app'
import { getFirestore } from 'firebase/firestore'
import { getAuth } from 'firebase/auth'

// Firebase configuration
// TODO: Replace with your actual Firebase config from Firebase Console
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || 'demo-api-key',
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || 'demo-project.firebaseapp.com',
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || 'demo-project-id',
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || 'demo-project.appspot.com',
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || '123456789',
  appId: import.meta.env.VITE_FIREBASE_APP_ID || 'demo-app-id',
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)

// Initialize Firebase services
export const db = getFirestore(app)
export const auth = getAuth(app)

// Development helper - log Firebase connection status
if (import.meta.env.DEV) {
  console.log('🔥 Firebase Configuration:')
  console.log('Project ID:', firebaseConfig.projectId)
  console.log('Auth Domain:', firebaseConfig.authDomain)

  if (firebaseConfig.projectId === 'demo-project-id') {
    console.warn('⚠️  Using demo Firebase config. Please set up your actual Firebase project!')
    console.log('📖 See FIREBASE_SETUP.md for instructions')
  } else {
    console.log('✅ Firebase configured successfully')
  }
}

export default app
